name: Build Theme on Release

permissions:
  contents: write

on:
  release:
    types: [created]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: master
          fetch-depth: 0  # Fetch full history
          submodules: recursive
          token: ${{ secrets.SUBMODULE_ACCESS_TOKEN }}

      - name: Update theme version
        run: |
          # Configure git user
          git config user.name github-actions
          git config user.email <EMAIL>
          
          # Extract the tag name without the 'v' prefix
          TAG_NAME=${GITHUB_REF#refs/tags/v}
          
          # Update the version number in style.css
          sed -i "s/Version: [0-9]*\.[0-9]*\.[0-9]*/Version: $TAG_NAME/" style.css
          sed -i "s/const DD_CHILD_THEME_VERSION = '[0-9]*\.[0-9]*\.[0-9]';/const DD_CHILD_THEME_VERSION = '$TAG_NAME';/" functions.php
          
          # Check if there are changes
          if [[ -n $(git status -s) ]]; then
            git add style.css
            git add functions.php
            git commit -m "Update theme version to $TAG_NAME"
            git push
          else
            echo "No changes to commit"
          fi

      - name: Configure Git submodule access
        run: |
          git config --global url."https://${{ secrets.SUBMODULE_ACCESS_TOKEN }}@github.com/".insteadOf "https://github.com/"

      - name: Build theme
        run: |
          # Ensure submodules are properly initialized
          git submodule update --init --recursive
          
          # Make the build script executable
          chmod +x setup/build.sh
          
          cd setup
          
          # Run the build script
          ./build.sh

      - name: Upload build as artifact
        uses: actions/upload-artifact@v4
        with:
          name: wordpress-theme
          path: setup/builds/

      - name: Upload build to release
        uses: softprops/action-gh-release@v1
        with:
          files: setup/builds/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}