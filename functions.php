<?php

const DD_CHILD_THEME_VERSION = '1.1.0';

add_action('wp_enqueue_scripts', function () {
    wp_enqueue_style('kuncer-investment-parent-theme', get_parent_theme_file_uri( 'style.css' ), ver: DD_CHILD_THEME_VERSION);
    wp_enqueue_style('morelowa-child-theme', get_stylesheet_directory_uri() . '/assets/css/style.css', ver: DD_CHILD_THEME_VERSION);
});


// Allow SVG
add_filter( 'wp_check_filetype_and_ext', function($data, $file, $filename, $mimes) {

  global $wp_version;
  if ( $wp_version !== '4.7.1' ) {
     return $data;
  }

  $filetype = wp_check_filetype( $filename, $mimes );

  return [
      'ext'             => $filetype['ext'],
      'type'            => $filetype['type'],
      'proper_filename' => $data['proper_filename']
  ];

}, 10, 4 );

function cc_mime_types( $mimes ){
  $mimes['svg'] = 'image/svg+xml';
  return $mimes;
}
add_filter( 'upload_mimes', 'cc_mime_types' );

function fix_svg() {
  echo '<style type="text/css">
        .attachment-266x266, .thumbnail img {
             width: 100% !important;
             height: auto !important;
        }
        </style>';
}
add_action( 'admin_head', 'fix_svg' );