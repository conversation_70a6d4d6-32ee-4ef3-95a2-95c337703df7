#!/bin/bash

# Wczytaj zmienne z pliku .env
if [ -f ../.env ]; then
    export $(grep -v '^#' ../.env | xargs)
else
    echo "Brak pliku .env w głównym folderze motywu!"
    exit 1
fi

# Spraw<PERSON><PERSON><PERSON>, czy zmienna SERVICE_NAME jest ustawiona
if [ -z "$SERVICE_NAME" ]; then
    echo "Brak zmiennej SERVICE_NAME w pliku .env!"
    exit 1
fi

if [ -d "builds" ]; then
    echo "Folder builds istnieje."
else
    mkdir "builds"
fi

cd ../

# Kompilowanie plików tłumaczeń
msgfmt languages/en_US.po -o languages/en_US.mo
msgfmt languages/pl_PL.po -o languages/pl_PL.mo

# Instalacja zależności Composer
composer install --no-dev

# Tworzenie pliku zip
ZIP_FILE="$SERVICE_NAME.zip"

echo "Tworzenie archiwum: $ZIP_FILE"

zip -r "setup/builds/$ZIP_FILE" ./ -x ".git/*" "composer.json" "composer.lock" "setup/*" ".idea/*" ".gitmodules" ".gitignore" ".github/*" ".env" ".gitattributes"

composer install

echo "Gotowe!"
