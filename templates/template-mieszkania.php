<?php
/**
 * Template Name: Mieszkania
 */

global $wp;

$levels_to_display = [
    'PARTER' => 'mieszkania/parter/',
    'PIĘTRO I' => 'mieszkania/pietro-i/',
    'PIĘTRO II' => 'mieszkania/pietro-ii/',
    'GARAŻ PODZIEMNY' => 'mieszkania/garaz-podziemny/',
];

$static_prices = [
    'Komórki lokatorskie:'      => '6 000 zł/m² z VAT',
    'Miejsce postojowe w garażu podziemnym:'   => '38 000 zł z VAT',
    'Miejsce postojowe zewnętrzne:'              => '17 000 zł z VAT',
];

$currentUrl = $wp->request;

get_header();
?>

<main id="main-content" class="single">
    <div class="container">
        <section class="mieszkania-template">
            <h1 class="section-title">Sprawdź dostępne <span class="highlight">mieszkania</span> w przekroju pięter</h1>

            <div class="pricing">
                <?php foreach ($static_prices as $label => $price): ?>
                    <div class="block">
                        <li class="info">
                            <span class="name"><?php echo esc_html($label); ?></span>
                            <span class="price"><?php echo esc_html($price); ?></span>
                        </li>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="left-right" id="rzuty">
                <div class="left">
                    <span class="title">Wybierz kondygnację:</span>
                    <div class="floor-selection">
                        <?php foreach ($levels_to_display as $name => $url): ?>
                            <a class="button <?php echo trim($currentUrl, '/') !== trim($url, '/') ? 'reverse' : ''; ?>"
                               href="<?php echo esc_url(home_url($url . '#rzuty')); ?>"><?php echo esc_html($name); ?></a>
                        <?php endforeach; ?>
                        <a class="more-locals-button" href="<?php echo esc_url(home_url('lista-lokali/')); ?>">Lista wszystkich lokali</a>
                    </div>
                </div>
                <div class="right">
                    <div class="caption">
                        <div class="legend">
                            <span class="legend-title">Legenda:</span>
                            <div class="legend-item dostepne">
                                <span class="color-box"></span>
                                <span class="legend-text">Dostępne</span>
                            </div>
                            <div class="legend-item rezerwacja">
                                <span class="color-box"></span>
                                <span class="legend-text">Rezerwacja</span>
                            </div>
                            <div class="legend-item sprzedane">
                                <span class="color-box"></span>
                                <span class="legend-text">Sprzedane</span>
                            </div>
                        </div>
                    </div>
                    <div class="floor-plan">
                        <img class="compass" src="<?php echo get_stylesheet_directory_uri(); ?>/public/compass.svg"
                             alt="Kompas">
                        <?php
                        if (function_exists('draw_mieszkania')) {
                            draw_mieszkania(get_post_meta(get_the_ID(), 'mieszkania_floor', true));
                        }

                        $shortcode = get_post_meta(get_the_ID(), 'mieszkania_shortcode', true);
                        if (!empty($shortcode)) {
                            echo do_shortcode($shortcode);
                        }
                        ?>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="container">
        <?php get_template_part('templates/view/featured-mieszkania', get_post_format()); ?>
    </div>

    <?php get_template_part('templates/view/cta-section', get_post_format()); ?>
</main>

<?php get_footer(); ?>