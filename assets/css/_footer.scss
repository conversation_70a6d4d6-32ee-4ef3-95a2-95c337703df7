footer {
  margin-top: 5rem;

  body:not(.page-template-template-kontakt) {
    margin: 0;
  }

  .footer-container {
    background-color: $color-light-gray;
    color: $color-black;
    padding: 5rem 0;

    .container {
      a {
        text-decoration: none;
        color: $color-black;

        &:hover,
        &:focus {
          text-decoration: underline;
        }
      }

      .left-right {
        flex-direction: column;

        .info {
          align-items: center;
          width: 100%;

          &.left-right {
            max-width: 100%;
            min-height: fit-content;

            .left {
              display: flex;
              flex-direction: row;
              gap: 8rem;
              height: 100%;
              font-size: $font-size-18px;

              .logo-container {
                img {
                  min-width: 205px;
                }
              }
            }
          }
        }

        hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid #ababab4d;
          margin: 1em 0;
          padding: 0;
        }

        .menus {
          color: $color-black;
          justify-content: space-between;

          .left {
            flex-basis: calc(25% - 10px);
            min-width: calc(25% - 10px);

            .top {
              display: flex;
              gap: 1.5rem;

              .social-links {
                font-size: $font-size-18px;
                line-height: 150%;
                font-weight: 600;
              }
            }

            .menu-item {
              padding: 0 !important;
            }
          }

          .right {
            display: flex;
            gap: 1rem;
            width: 100%;
            justify-content: space-between;
            flex-basis: calc(75% - 10px);

            .footer-menu {
              flex-basis: calc(25% - 25px);

              p {
                display: flex;
                flex-direction: column;
                gap: 8px;
              }

              &:not(:nth-of-type(2)) {
                ul {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;

                  li {
                    padding: 0;
                  }
                }
              }

              &:nth-of-type(2) {
                flex-basis: calc(35% - 25px);
              }
            }
          }

          .footer-menu {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;

            img {
              max-width: 45px;
            }

            &-2 {
              .menu-item {
                max-width: 200px;
              }
            }

            &-2,
            &-3,
            &-4 {
              display: flex;
              flex-direction: column;
              gap: 10px;
            }

            &-5 {
              display: flex;
              flex-direction: column;
              gap: 16px;
            }
          }
        }

        .menu-title {
          font-size: $font-size-20px;
          font-weight: 600;
        }

        .footer-menu {
          word-break: break-word;
          width: 100%;
        }
      }

      li {
        padding-bottom: 16px;
      }
    }
  }
}

@media (max-width: $media-laptop) {
  footer {
    .footer-container {
      .container {
        .left-right {
          flex-wrap: wrap;
          gap: 1rem;

          .info {
            &.left-right {
              max-width: 100%;
            }
          }

          .menus {
            flex-direction: column-reverse;
            gap: 2.5rem;

            .left {
              flex-basis: 100%;
              min-width: 100%;
            }

            .right {
              flex-wrap: wrap;

              .footer-menu {
                flex-basis: calc(50% - 10px);

                &:nth-of-type(2) {
                  flex-basis: calc(50% - 10px);
                }
              }
            }

            .footer-menu {
              flex-basis: 100%;

              br {
                display: none;
              }

              &-5 {
                flex-direction: row;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: $media-tablet) {
  footer {
    .footer-container {
      .container {
        .left-right {
          gap: 15px;

          .menus {
            flex-direction: column-reverse;
            gap: 2.5rem;

            .right {
              .footer-menu {
                flex-basis: 100%;

                &:nth-of-type(2) {
                  flex-basis: 100%;
                }
              }
            }

            .footer-menu {
              flex-basis: 100%;

              br {
                display: none;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: $media-mobile) {
  footer {
    .footer-container {
      padding: 3rem 0;

      .container {
        .left-right {
          gap: 15px;

          .info {
            .left.side {
              flex-direction: column;
              gap: 1rem;
            }
          }

          .menus {
            .footer-menu {
              flex-basis: 100%;

              &:first-of-type {
                flex-basis: 100%;
                padding-top: 1rem;
                order: 5;
              }

              &-5 {
                display: flex;
                flex-wrap: wrap;
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: $media-mobile-sm) {
}
