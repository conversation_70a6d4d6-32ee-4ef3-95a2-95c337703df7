.menu-container {
  .menu {
    .main-menu {
      gap: 10px;

      li {
        a {
          padding: 5px 10px;
        }
      }
    }
    .logo-container {
      transform: unset;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
  .mobile-container {
    .hamburger-toggle-container {
      .hamburger-toggle-bar,
      .hamburger-toggle-bar::before,
      .hamburger-toggle-bar::after {
        height: 1px;
      }
    }
  }

  .menu-container {
    .menu {
      .right {
        flex-direction: column;
        align-items: start;
        gap: 0;

        .main-menu {
          li {
            margin-bottom: 34px;

            a {
              padding: 0;
            }
          }
        }
      }
    }
  }
}
@media (max-width: $media-mobile) {
}
@media (max-width: $media-mobile-sm) {
}
