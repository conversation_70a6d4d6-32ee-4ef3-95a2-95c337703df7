@import "_variables";
@import "../../../kuncer-investment-parent-theme/assets/css/style";
@import "topbar";
@import "frontpage";
@import "shortcode";
@import "footer";

.highlight {
  border-radius: 35px;
  padding: 5px 20px;
}

strong {
  font-weight: 700;
}

.swiper-button-next::after {
  filter: $filter;
}

.wpcf7-full-width input[type="submit"] {
  background-color: #ffffff99;
}

.section-map {
  .left-right {
    .left {
      .section-title {
        max-width: 80%;
      }
    }
  }
}

.section-subtitle {
  font-size: $font-size-16px;
  font-family: $font-secondary;
  color: $color-primary;
  text-transform: uppercase;
  font-weight: 400;
  letter-spacing: 2px;
  line-height: 24px;
  margin-bottom: -15px;
}

//COMPONENT STYLES
.section-cta {
  position: relative;
  background-image: url("../../public/cta-background.webp");
  background-position: 100% 55%;
  background-size: cover;

  &:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(120deg, #000000, #000000);
    opacity: 0.6;
    z-index: 2;
  }
  .container {
    min-height: 500px;
    position: relative;
    z-index: 3;
  }
}

.homepage-content {
  background-image: url("../../public/hallera_front.webp");
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
  .section-map {
  .left-right {
    .left {
      .section-title {
        max-width: 100%;
      }
    }
  }
}
}
@media (max-width: $media-mobile) {
}
@media (max-width: $media-mobile-sm) {
}
