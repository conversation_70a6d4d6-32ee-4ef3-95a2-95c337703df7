.dd_clipboard-button {
  svg {
    path {
      stroke: $color-black;
    }
  }

  .clipboard-status {
    color: $color-black;
  }
}

.mieszkania-template {
  .pricing {
    .block {
      .info {
        flex-direction: row;
        gap: 10px;
      }
    }
  }

  .left-right {
    padding-top: 4rem;
  }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
  .mieszkania-template {
  .pricing {
    .block {
      .info {
        flex-direction: column;
      }
    }
  }
}
}
@media (max-width: $media-mobile) {
}
@media (max-width: $media-mobile-sm) {
}

