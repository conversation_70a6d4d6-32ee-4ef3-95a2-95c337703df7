.main-hero {
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("../../public/hero-image.webp");
  max-height: 650px;
  min-height: 600px;
  display: flex;
  align-items: center;

  .left-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 60px;
    width: 100%;
    z-index: 1;
  }

  .left-side {
    display: flex;
    align-items: center;
    max-width: 450px;
    min-height: 650px;

    &::before {
      background-color: rgb(0 0 0 / 40%);
      content: "";
      position: absolute;
      height: 100%;
      left: 50%;
      margin-left: -50vw;
      margin-right: -50vw;
      max-width: 42vw;
      top: 0;
      width: 100vw;
      z-index: -1;
    }

    .main-content {
      font-family: $font-secondary;
      color: $color-white;

      .subtitle {
        font-size: $font-size-18px;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .main-title {
        font-size: $font-size-48px;
        margin: 20px 0;
        line-height: 120%;
        font-weight: 400;
        max-width: 450px;
        font-family: $font-secondary;
      }

      .button {
        width: fit-content;
        color: $color-black;
      }
    }
  }
}

.section-settle {
  .container {
    .content {
      p {
        color: $color-text;
      }
    }
  }
}

.section-visualization {
  .left-right {
    .left {
      .section-title {
        font-family: $font-secondary;
        line-height: 120%;
        letter-spacing: -0.02em;
      }
    }

    .right {
      .items {
        .item {
          .link {
            svg {
              path {
                stroke: $color-primary;
              }
            }
          }
        }
      }
    }
  }
}

.section-investments {
  padding: 2.5rem 0 7.5rem 0;
  background-image: url("../../public/investments-vector.svg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center top;

  min-height: 500px;

  .content {
    gap: 40px;

    .items {
      .item {
        gap: 16px;

        img {
          &:hover,
          &:focus {
            filter: unset;
          }
        }

        .sub-title {
          font-size: $font-size-24px;
          line-height: 130%;
        }

        .description {
          color: $color-text;
          line-height: 155%;
        }
      }
    }
  }
}

.section-location {
  .section-subtitle {
    display: flex;
    justify-content: center;
    margin-bottom: 8px;
  }
  .left-right {
    justify-content: space-between;

    p {
      font-size: $font-size-18px;
      color: $color-text;
      line-height: 155%;
    }
  }
}

.section-schedule {
  padding: 0;
}

.section-comfort {
  background-color: $color-primary;
  color: $color-white;

  .section-subtitle {
    color: $color-white;
  }

  .section-title {
    width: 80%;
  }

  .left-right {
    .left {
      gap: 40px;

      p {
        font-size: $font-size-18px;
        line-height: 155%;
      }
    }
    .right {
      .items {
        .item {
          &:nth-child(2) {
            border-block: 0.5px solid rgba(255, 255, 255, 0.4);
          }
        }
      }
    }
  }
}

.section-image {
  position: relative;
  background-image: url("../../public/section-image.webp");
  background-position: 100% 30%;
  .vector-overlay {
    position: absolute;
    top: 0;
    pointer-events: none;

    &.left {
      width: 55%;
      left: 0;
    }

    &.right {
      width: 30%;
      right: 0;
    }
  }
}

.section-frontmap {
  margin-bottom: 7.5rem;
  overflow: hidden;
  background-color: $color-secondary;

  .left-right {
    .left {
      flex-basis: calc(45% - 25px);
      align-content: center;

      .section-subtitle {
        color: $color-text;
      }

      .section-title {
        font-size: $font-size-54px;
      }
    }

    .right {
      flex: 1;
      margin-right: -100vw;
      max-width: 52vw;
      position: relative;
      right: -2%;
      width: 52vw;
      display: flex;
    }
  }
}

.section-featured {
  .section-subtitle {
    display: flex;
    justify-content: center;
    margin-bottom: 0;
  }
}

@media (max-width: $media-hd) {
  .section-frontmap {
    .left-right {
      .right {
        right: -3%;
      }
    }
  }
}
@media (max-width: $media-laptop) {
  .main-hero {
    .left-side {
      .main-content {
        .main-title {
          max-width: 350px;
          font-size: $font-size-32px;
        }
      }
    }
  }
}
@media (max-width: $media-tablet) {
  .main-hero {
    .left-side {
      &::before {
        height: 52%;
        max-width: 100%;
        right: 50%;
        top: unset;
        bottom: 0;
      }

      .main-content {
        position: absolute;
        min-height: 250px;
        bottom: 40px;

        .main-title {
          max-width: 95%;
          font-size: $font-size-32px;
        }

        .button {
          text-align: center;
        }
      }
    }
  }

  .section-settle {
    .container {
      .content {
        .section-title {
          font-size: $font-size-38px;
        }
      }
    }
  }

  .section-investments {
    background-image: unset;

    .content {
      .section-title {
        font-size: $font-size-38px;
      }
    }
  }

  .section-frontmap {
    margin-bottom: 3.75rem;

    .left-right {
      flex-direction: column-reverse;

      &.container {
        padding: 0;
      }

      .left {
        flex-basis: 100%;
        padding: 2rem;

        .section-title {
          font-size: $font-size-38px;

          br {
            display: none;
          }
        }
      }

      .right {
        right: unset;
        max-width: 100vw;
        width: 100vw;

        .wpgmza_map {
          margin-left: 0 !important;
        }
      }
    }
  }

  .section-comfort {
    .section-title {
      width: 100%;
      font-size: $font-size-38px;
    }
  }

  .section-location {
    .section-title {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: 40px;
      font-size: $font-size-38px;

      .highlight {
        width: fit-content;
      }
    }
  }

  .section-schedule {
    .left-right {
      .left {
        .section-title {
          font-size: $font-size-38px;
          text-align: left;
        }
      }
    }
  }

  .section-visualization {
    .left-right {
      gap: 40px;

      .left {
        .section-title {
          font-size: $font-size-38px;
        }
      }
    }
  }

  .section-cta {
    .container {
      .content {
        .section-title {
          font-size: $font-size-38px;
        }
      }
    }
  }
}
@media (max-width: $media-mobile) {
  .section-frontmap {
    .left-right {
      .left {
        .section-title {
          br {
            display: flex;
          }
        }
      }
    }
  }
}
@media (max-width: $media-mobile-sm) {
  .main-hero {
    .left-side {
      &::before {
        height: 55%;
      }

      .main-content {
        min-height: 250px;

        .main-title {
          font-size: $font-size-28px;
        }
      }
    }
  }
}
